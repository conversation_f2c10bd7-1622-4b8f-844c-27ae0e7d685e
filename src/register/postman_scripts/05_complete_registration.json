{"info": {"name": "Complete Registration", "description": "Test hoàn tất đăng ký", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Complete Registration - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 201', function () {", "    pm.response.to.have.status(201);", "});", "", "pm.test('Response has success message and userId', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson).to.have.property('userId');", "    pm.expect(responseJson.message).to.include('thành công');", "    pm.expect(responseJson.userId).to.be.a('string');", "});", "", "pm.test('Save userId for future tests', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('registeredUserId', responseJson.userId);", "    console.log('User registered successfully with ID:', responseJson.userId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{verified_sessionId}}\",\n  \"name\": \"Nguyễn Văn Test\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"<PERSON>\",\n  \"address\": \"123 Đường Test, Quận 1, TP.HCM\",\n  \"citizenId\": \"123456789012\",\n  \"avatar\": \"https://example.com/avatar.jpg\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}, {"name": "Complete Registration - Invalid Session", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404', function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test('Error message about invalid session', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('Session không tồn tại');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"invalid-session-id\",\n  \"name\": \"Nguyễn Văn Test\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"<PERSON>\",\n  \"address\": \"123 Đường Test, Quận 1, TP.HCM\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}, {"name": "Complete Registration - Not Verified", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about not verified', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('<PERSON><PERSON><PERSON> xác thực <PERSON>');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"name\": \"Nguyễn Văn Test\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"<PERSON>\",\n  \"address\": \"123 Đường Test, Quận 1, TP.HCM\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}, {"name": "Complete Registration - Missing Required Fields", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{verified_sessionId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}, {"name": "Complete Registration - Invalid Gender", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error for invalid gender', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{verified_sessionId}}\",\n  \"name\": \"Nguyễn Văn Test\",\n  \"dateOfBirth\": \"1990-01-01\",\n  \"gender\": \"InvalidGender\",\n  \"address\": \"123 Đường Test, Quận 1, TP.HCM\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}, {"name": "Complete Registration - Invalid Date Format", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error for invalid date', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{verified_sessionId}}\",\n  \"name\": \"Nguyễn Văn Test\",\n  \"dateOfBirth\": \"invalid-date\",\n  \"gender\": \"Nam\",\n  \"address\": \"123 Đường Test, Quận 1, TP.HCM\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/register/complete", "host": ["{{baseUrl}}"], "path": ["register", "complete"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}