{"id": "revita-registration-env", "name": "Revita Registration Environment", "values": [{"key": "baseUrl", "value": "http://localhost:3000", "description": "Base URL của API server", "enabled": true}, {"key": "sessionId", "value": "", "description": "Session ID từ phone registration", "enabled": true}, {"key": "sessionId_email", "value": "", "description": "Session ID từ email registration", "enabled": true}, {"key": "verified_sessionId", "value": "", "description": "Session ID sau khi verify OTP thành công", "enabled": true}, {"key": "registeredUserId", "value": "", "description": "User ID sau khi đăng ký thành công", "enabled": true}, {"key": "test_otp", "value": "123456", "description": "OTP để test (cầ<PERSON> cập nhật với OTP thực từ console/email/SMS)", "enabled": true}, {"key": "current_otp", "value": "", "description": "OTP hiện tại đ<PERSON><PERSON><PERSON> sử dụng trong request", "enabled": true}, {"key": "test_phone", "value": "0987654321", "description": "<PERSON><PERSON> điện thoại để test", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "description": "<PERSON>ail đ<PERSON> test", "enabled": true}, {"key": "test_name", "value": "<PERSON><PERSON><PERSON><PERSON>", "description": "Tên để test registration", "enabled": true}, {"key": "test_password", "value": "password123", "description": "<PERSON><PERSON><PERSON> khẩu để test", "enabled": true}], "_postman_variable_scope": "environment"}