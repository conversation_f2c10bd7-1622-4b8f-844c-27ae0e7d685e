{"info": {"name": "Register Step 1 - Phone", "description": "Test đăng ký bước 1 với số điện thoại", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Register Step 1 - Phone Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sessionId and message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('sessionId');", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.sessionId).to.be.a('string');", "    pm.expect(responseJson.message).to.include('OTP');", "});", "", "pm.test('Save sessionId for next request', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('sessionId', responseJson.sessionId);", "    console.log('SessionId saved:', responseJson.sessionId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0987654321\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}, {"name": "Register Step 1 - Phone Already Exists", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 409', function () {", "    pm.response.to.have.status(409);", "});", "", "pm.test('Error message about phone already exists', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('đã được đăng ký');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0987654321\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}, {"name": "Register Step 1 - Invalid Phone Format", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"invalid-phone\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}, {"name": "Register Step 1 - Missing Phone and Email", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error for missing fields', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}