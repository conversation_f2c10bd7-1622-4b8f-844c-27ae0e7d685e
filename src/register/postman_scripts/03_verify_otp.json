{"info": {"name": "Verify OTP", "description": "Test xác th<PERSON>c <PERSON>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Verify OTP - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sessionId and success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('sessionId');", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('thành công');", "});", "", "pm.test('Update sessionId for next step', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('verified_sessionId', responseJson.sessionId);", "    console.log('Verified SessionId saved:', responseJson.sessionId);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": ["// L<PERSON>y sessionId từ environment (từ step 1)", "const sessionId = pm.environment.get('sessionId') || pm.environment.get('sessionId_email');", "if (!sessionId) {", "    console.log('Warning: No sessionId found. Please run step 1 first.');", "}", "", "// Prompt user to enter OTP (trong thực tế sẽ lấy từ SMS/Email)", "// Để test, có thể hardcode OTP hoặc lấy từ console log", "const otp = pm.environment.get('test_otp') || '123456';", "pm.environment.set('current_otp', otp);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"otp\": \"{{current_otp}}\"\n}"}, "url": {"raw": "{{baseUrl}}/register/verify-otp", "host": ["{{baseUrl}}"], "path": ["register", "verify-otp"]}}, "response": []}, {"name": "Verify OTP - Wrong OTP", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about wrong OTP', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('không chính xác');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\",\n  \"otp\": \"000000\"\n}"}, "url": {"raw": "{{baseUrl}}/register/verify-otp", "host": ["{{baseUrl}}"], "path": ["register", "verify-otp"]}}, "response": []}, {"name": "Verify OTP - Invalid Session", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404', function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test('Error message about invalid session', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('Session không tồn tại');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"invalid-session-id\",\n  \"otp\": \"123456\"\n}"}, "url": {"raw": "{{baseUrl}}/register/verify-otp", "host": ["{{baseUrl}}"], "path": ["register", "verify-otp"]}}, "response": []}, {"name": "Verify OTP - <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/register/verify-otp", "host": ["{{baseUrl}}"], "path": ["register", "verify-otp"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}