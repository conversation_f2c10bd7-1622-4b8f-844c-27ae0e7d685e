{"info": {"name": "Register Step 1 - Email", "description": "Test đăng ký bước 1 với email", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Register Step 1 - Email Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has sessionId and message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('sessionId');", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.sessionId).to.be.a('string');", "    pm.expect(responseJson.message).to.include('email');", "});", "", "pm.test('Save sessionId for next request', function () {", "    const responseJson = pm.response.json();", "    pm.environment.set('sessionId_email', responseJson.sessionId);", "    console.log('SessionId (email) saved:', responseJson.sessionId);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}, {"name": "Register Step 1 - Email Already Exists", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 409', function () {", "    pm.response.to.have.status(409);", "});", "", "pm.test('Error message about email already exists', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('đã được đăng ký');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}, {"name": "Register Step 1 - Invalid Email Format", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Validation error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"invalid-email\"\n}"}, "url": {"raw": "{{baseUrl}}/register/step1", "host": ["{{baseUrl}}"], "path": ["register", "step1"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}