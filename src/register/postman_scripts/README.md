# Postman Test Scripts cho Registration Flow

Thư mục này chứa các test script JSON cho Postman để test toàn bộ flow đăng ký của hệ thống.

## Cấu trúc Test Scripts

### 1. `01_register_step1_phone.json`
Test đăng ký bước 1 với số điện thoại:
- ✅ Đăng ký thành công với số điện thoại hợp lệ
- ❌ Số điện thoại đã tồn tại
- ❌ Định dạng số điện thoại không hợp lệ
- ❌ Thiếu cả phone và email

### 2. `02_register_step1_email.json`
Test đăng ký bước 1 với email:
- ✅ Đăng ký thành công với email hợp lệ
- ❌ Email đã tồn tại
- ❌ Định dạng email không hợp lệ

### 3. `03_verify_otp.json`
Test xác thực OTP:
- ✅ <PERSON><PERSON><PERSON> thực OTP thành công
- ❌ OTP không chính xác
- ❌ Session không hợp lệ
- ❌ Thiếu thông tin bắt buộc

### 4. `04_resend_otp.json`
Test gửi lại OTP:
- ✅ Gửi lại OTP thành công
- ❌ Session không hợp lệ
- ❌ OTP đã được xác thực

### 5. `05_complete_registration.json`
Test hoàn tất đăng ký:
- ✅ Hoàn tất đăng ký thành công
- ❌ Session không hợp lệ
- ❌ Chưa xác thực OTP
- ❌ Thiếu thông tin bắt buộc
- ❌ Giới tính không hợp lệ
- ❌ Định dạng ngày sinh không hợp lệ

## Cách sử dụng

### 1. Import vào Postman
1. Mở Postman
2. Click "Import" 
3. Chọn từng file JSON hoặc import cả thư mục
4. Các collection sẽ được tạo tự động

### 2. Thiết lập Environment
Tạo một Environment trong Postman với các biến sau:

```json
{
  "baseUrl": "http://localhost:3000",
  "sessionId": "",
  "sessionId_email": "",
  "verified_sessionId": "",
  "registeredUserId": "",
  "test_otp": "123456",
  "current_otp": ""
}
```

### 3. Chạy Test theo thứ tự

#### Bước 1: Test Registration Step 1
- Chạy `01_register_step1_phone.json` hoặc `02_register_step1_email.json`
- Script sẽ tự động lưu `sessionId` vào environment

#### Bước 2: Lấy OTP từ Console/Email/SMS
- Kiểm tra console log của server để lấy OTP
- Hoặc kiểm tra email/SMS nếu đã cấu hình
- Cập nhật biến `test_otp` trong environment với OTP thực

#### Bước 3: Test Verify OTP
- Chạy `03_verify_otp.json`
- Script sẽ tự động lưu `verified_sessionId`

#### Bước 4: Test Complete Registration
- Chạy `05_complete_registration.json`
- Script sẽ lưu `registeredUserId` khi thành công

#### Bước 5: Test Resend OTP (Optional)
- Chạy `04_resend_otp.json` để test chức năng gửi lại OTP

## Lưu ý quan trọng

### Environment Variables
- `sessionId`: Được lưu từ step 1 (phone registration)
- `sessionId_email`: Được lưu từ step 1 (email registration)  
- `verified_sessionId`: Được lưu sau khi verify OTP thành công
- `test_otp`: OTP để test (mặc định 123456, cần cập nhật với OTP thực)

### Test Data
Các test sử dụng dữ liệu mẫu:
- Phone: `0987654321`
- Email: `<EMAIL>`
- Name: `Nguyễn Văn Test`
- Password: `password123`

### Error Handling
Mỗi test case đều kiểm tra:
- Status code chính xác
- Response structure
- Error messages phù hợp
- Data validation

### Development vs Production
- Trong development: OTP sẽ được log ra console
- Trong production: OTP sẽ được gửi qua SMS/Email thực

## Troubleshooting

### Lỗi thường gặp:

1. **"No sessionId found"**
   - Chạy lại step 1 để tạo session mới
   - Kiểm tra environment variables

2. **"Session không tồn tại"**
   - Session đã hết hạn (30 phút)
   - Tạo session mới từ step 1

3. **"OTP không chính xác"**
   - Kiểm tra console log để lấy OTP đúng
   - OTP có thời hạn 5 phút

4. **"Chưa xác thực OTP"**
   - Phải chạy verify OTP trước khi complete registration
   - Sử dụng `verified_sessionId` thay vì `sessionId`

### Debug Tips:
- Bật Console trong Postman để xem logs
- Kiểm tra Response body để hiểu lỗi
- Verify environment variables được set đúng
- Kiểm tra server logs để debug backend issues

## Automation

Có thể chạy toàn bộ flow tự động bằng Newman:

```bash
# Install Newman
npm install -g newman

# Run collection
newman run 01_register_step1_phone.json -e environment.json
newman run 03_verify_otp.json -e environment.json
newman run 05_complete_registration.json -e environment.json
```

## Mở rộng

Có thể thêm các test case khác:
- Test với multiple sessions
- Test concurrent registrations
- Test rate limiting
- Test với dữ liệu Unicode
- Performance testing với nhiều requests
