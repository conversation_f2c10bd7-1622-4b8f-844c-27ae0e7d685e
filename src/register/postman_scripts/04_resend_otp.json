{"info": {"name": "Resend OTP", "description": "Test gửi lại OTP", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Resend OTP - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has success message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.include('OTP mới đã được gửi');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{sessionId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/register/resend-otp", "host": ["{{baseUrl}}"], "path": ["register", "resend-otp"]}}, "response": []}, {"name": "Resend OTP - Invalid Session", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404', function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test('Error message about invalid session', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('Session không tồn tại');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"invalid-session-id\"\n}"}, "url": {"raw": "{{baseUrl}}/register/resend-otp", "host": ["{{baseUrl}}"], "path": ["register", "resend-otp"]}}, "response": []}, {"name": "Resend OTP - Already Verified", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Error message about already verified', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.message).to.include('đã được xác thực');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"sessionId\": \"{{verified_sessionId}}\"\n}"}, "url": {"raw": "{{baseUrl}}/register/resend-otp", "host": ["{{baseUrl}}"], "path": ["register", "resend-otp"]}}, "response": []}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}]}